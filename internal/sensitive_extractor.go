package internal

import (
	"encoding/json"
	"os"
	"path/filepath"
	"strings"

	"github.com/dop251/goja"
)

// OriginalFindSomethingExtractor 基于goja引擎的敏感信息提取器
// 使用原版findsomething的JavaScript代码进行敏感信息提取
type OriginalFindSomethingExtractor struct {
	vm *goja.Runtime // goja JavaScript运行时
}

// NewOriginalFindSomethingExtractor 创建新的敏感信息提取器实例
// 初始化goja运行时并加载JavaScript提取代码
//
// 返回值：
//   - *OriginalFindSomethingExtractor: 敏感信息提取器实例
func NewOriginalFindSomethingExtractor() *OriginalFindSomethingExtractor {
	vm := goja.New()

	// 尝试读取JavaScript提取代码
	var jsCode []byte
	var err error

	// 尝试从assets目录读取background.js
	jsPath := filepath.Join("assets", "background.js")
	jsCode, err = os.ReadFile(jsPath)
	if err != nil {
		// 如果读取失败，使用内置的简化版本
		jsCode = []byte(getBuiltinExtractorCode())
	}

	// 创建适配的JavaScript环境
	adaptedCode := adaptJSForGoja(string(jsCode))

	// 在goja中执行JavaScript代码
	_, err = vm.RunString(adaptedCode)
	if err != nil {
		// 如果执行失败，使用备用的简化版本
		vm.RunString(getBuiltinExtractorCode())
	}

	return &OriginalFindSomethingExtractor{
		vm: vm,
	}
}

// ExtractSensitiveInfo 从给定的内容中提取敏感信息
// 使用JavaScript引擎执行提取逻辑，完全兼容findsomething的提取规则
//
// 参数：
//   - content: 要分析的内容（HTML或JavaScript代码）
//   - sourceURL: 内容来源URL
//
// 返回值：
//   - *FindSomethingResult: 提取到的敏感信息结果
func (e *OriginalFindSomethingExtractor) ExtractSensitiveInfo(content, sourceURL string) *FindSomethingResult {
	if e.vm == nil {
		return nil
	}
	
	// 设置全局变量
	e.vm.Set("inputData", content)
	e.vm.Set("sourceURL", sourceURL)
	
	// 执行提取函数
	result, err := e.vm.RunString(`
		(function() {
			try {
				// 使用extract_info函数提取敏感信息
				var extractedData = extract_info(inputData);
				
				// 构建符合FindSomethingResult格式的结果
				var result = {
					current: sourceURL,
					done: "completed",
					tasklist: [],
					donetasklist: [],
					pretasknum: 1,
					source: {},
					ip: extractedData.ip || [],
					ip_port: extractedData.ip_port || [],
					domain: extractedData.domain || [],
					path: extractedData.path || [],
					incomplete_path: extractedData.incomplete_path || [],
					url: extractedData.url || [],
					sfz: extractedData.sfz || [],
					mobile: extractedData.mobile || [],
					mail: extractedData.mail || [],
					jwt: extractedData.jwt || [],
					algorithm: extractedData.algorithm || [],
					secret: extractedData.secret || [],
					static: extractedData.static || []
				};
				
				// 设置source映射
				result.source[sourceURL] = sourceURL;
				
				return result;
			} catch (e) {
				// 如果提取失败，返回空结果
				return {
					current: sourceURL,
					done: "error",
					tasklist: [],
					donetasklist: [],
					pretasknum: 0,
					source: {},
					ip: [],
					ip_port: [],
					domain: [],
					path: [],
					incomplete_path: [],
					url: [],
					sfz: [],
					mobile: [],
					mail: [],
					jwt: [],
					algorithm: [],
					secret: [],
					static: []
				};
			}
		})()
	`)
	
	if err != nil {
		return nil
	}
	
	// 将JavaScript结果转换为Go结构体
	resultObj := result.Export()
	jsonBytes, err := json.Marshal(resultObj)
	if err != nil {
		return nil
	}
	
	var findSomethingResult FindSomethingResult
	err = json.Unmarshal(jsonBytes, &findSomethingResult)
	if err != nil {
		return nil
	}
	
	return &findSomethingResult
}

// adaptJSForGoja 适配JavaScript代码以在goja环境中运行
// 移除浏览器特定的API调用，保留核心提取逻辑
func adaptJSForGoja(jsCode string) string {
	// 移除Chrome扩展相关的代码
	adapted := strings.ReplaceAll(jsCode, "chrome.runtime.onMessage.addListener", "// chrome.runtime.onMessage.addListener")
	adapted = strings.ReplaceAll(adapted, "chrome.tabs.onUpdated.addListener", "// chrome.tabs.onUpdated.addListener")
	adapted = strings.ReplaceAll(adapted, "chrome.tabs.onActivated.addListener", "// chrome.tabs.onActivated.addListener")
	adapted = strings.ReplaceAll(adapted, "chrome.storage.local.set", "// chrome.storage.local.set")
	
	// 移除fetch相关的异步代码
	adapted = strings.ReplaceAll(adapted, "fetch(", "// fetch(")
	adapted = strings.ReplaceAll(adapted, "Promise.all", "// Promise.all")
	
	// 移除console.log（可选）
	adapted = strings.ReplaceAll(adapted, "console.log", "// console.log")
	
	return adapted
}

// getBuiltinExtractorCode 获取内置的简化版敏感信息提取代码
// 当无法加载外部JavaScript文件时使用此备用版本
func getBuiltinExtractorCode() string {
	return `
// 内置的敏感信息提取器（简化版）
var nuclei_regex = [
    /["']?(?:A3T[A-Z0-9]|AKIA|AGPA|AIDA|AROA|AIPA|ANPA|ANVA|ASIA)[A-Z0-9]{16}["']?/g,
    /AIza[0-9A-Za-z_\-]{35}/g,
    /[Bb]earer\s+[a-zA-Z0-9\-=._+/\\]{20,500}/g,
    /[Bb]asic\s+[A-Za-z0-9+/]{18,}={0,2}/g,
    /(glpat-[a-zA-Z0-9\-=_]{20,22})/g,
    /((?:ghp|gho|ghu|ghs|ghr|github_pat)_[a-zA-Z0-9_]{36,255})/g,
    /["'](wx[a-z0-9]{15,18})["']/g,
    /["'](ww[a-z0-9]{15,18})["']/g,
    /eyJrIjoi[a-zA-Z0-9\-_+/]{50,100}={0,2}/g
];

function get_secret(data) {
    var result = [];
    for (var i = nuclei_regex.length - 1; i >= 0; i--) {
        var tmp_result = data.match(nuclei_regex[i]);
        if (tmp_result != null){
            for(var j in tmp_result){
                result.push(tmp_result[j]);
            }
        }
    }
    return result;
}

function extract_info(data) {
    var extract_data = {};
    extract_data['sfz'] = data.match(/['"]((\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(\d{6}(18|19|20)\d{2}(0[1-9]|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)))['"]/g);
    extract_data['mobile'] = data.match(/['"](1(3([0-35-9]\d|4[1-8])|4[14-9]\d|5([\d]\d|7[1-79])|66\d|7[2-35-8]\d|8\d{2}|9[89]\d)\d{7})['"]/g);
    extract_data['mail'] = data.match(/['"][a-zA-Z0-9\._\-]*@[a-zA-Z0-9\._\-]{1,63}\.((?!js|css|jpg|jpeg|png|ico)[a-zA-Z]{2,})['"]/g);
    extract_data['ip'] = data.match(/['"](([a-zA-Z0-9]+:)?\/\/)?\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(\/.*?)?['"]/g);
    extract_data['ip_port'] = data.match(/['"]\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d{1,5}['"]/g);
    extract_data['domain'] = data.match(/['"][a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+['"]/g);
    extract_data['path'] = data.match(/['"][\/][a-zA-Z0-9\/_\-\.]*['"]/g);
    extract_data['incomplete_path'] = data.match(/['"][a-zA-Z0-9][a-zA-Z0-9\/_\-\.]*[\/][a-zA-Z0-9\/_\-\.]*['"]/g);
    extract_data['url'] = data.match(/['"]https?:\/\/[a-zA-Z0-9\.\-]+[a-zA-Z0-9\.\-\/\?\&\=\%\#]*['"]/g);
    extract_data['jwt'] = data.match(/['"](ey[A-Za-z0-9_-]{10,}\.[A-Za-z0-9._-]{10,}|ey[A-Za-z0-9_\/+-]{10,}\.[A-Za-z0-9._\/+-]{10,})['"]/g);
    extract_data['algorithm'] = data.match(/\W(Base64\.encode|Base64\.decode|btoa|atob|CryptoJS\.AES|CryptoJS\.DES|JSEncrypt|rsa|KJUR|$\.md5|md5|sha1|sha256|sha512)[\(\.]/gi);
    extract_data['secret'] = get_secret(data);
    extract_data['static'] = [];
    
    // 清理null值
    for (var key in extract_data) {
        if (extract_data[key] === null) {
            extract_data[key] = [];
        }
    }
    
    return extract_data;
}
`
}
